"""
翻译API接口层 - 简化版本，只保留上传接口
"""

# 保存上传的文件到临时目录
import tempfile
import os
from flask import Blueprint, request, jsonify
import logging
from app.services.excel_service import ExcelService
from app.services.translate_service import TranslateService

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
translate_bp = Blueprint("translate", __name__)


@translate_bp.route("/translate/upload", methods=["POST"])
def upload_excel():
    """Excel文件上传解析接口 - 只解析不翻译"""
    try:
        # 检查是否有文件上传
        if "file" not in request.files:
            return jsonify({"code": -1, "message": "没有上传文件"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"code": -1, "message": "文件名为空"}), 400

        excel_service = ExcelService()
        translate_service = TranslateService()

        logger.info(f"开始处理上传文件: {file.filename}")

        file_path = excel_service.create_temp_file(file)
        logger.info(f"临时文件路径: {file_path}")

        # 解析Excel文件
        parse_result = excel_service.parse_excel_to_translation_tasks(file_path)
        logger.info(f"Excel解析完成，共 {len(parse_result['list'])} 条记录")

        # 翻译语言代码为中文名称
        language_names = {}
        if parse_result.get("target_language_list"):
            try:
                language_names_json = translate_service.translate_language_code_list(
                    parse_result["target_language_list"]
                )
                import json

                language_names = json.loads(language_names_json)
                logger.info(f"语言名称翻译完成: {language_names}")
            except Exception as e:
                logger.warning(f"语言名称翻译失败: {str(e)}")
                # 使用默认的语言代码作为名称
                for lang_info in parse_result["target_language_list"]:
                    language_names[lang_info["code"]] = lang_info["code"]

        # 转换为前端需要的格式
        languages = [parse_result["source_langauge"]]  # 源语言放在第一位
        for lang_info in parse_result["target_language_list"]:
            languages.append(lang_info["code"])

        # 构建翻译数据字典
        translations = {}
        for task in parse_result["list"]:
            key = task["key"]
            translations[key] = {}

            # 添加源语言内容
            translations[key][parse_result["source_langauge"]] = task[
                "source_language_context"
            ]

            # 添加目标语言内容（初始为空）
            for translate_item in task["translate_list"]:
                translations[key][translate_item["language"]] = translate_item[
                    "content"
                ]

        # 构建元数据
        metadata = {
            "filename": file.filename,
            "total_keys": len(parse_result["list"]),
            "total_languages": len(languages),
            "sheet_name": "Sheet1",  # 默认工作表名
        }

        # 构建前端需要的数据格式
        result_data = {
            "languages": languages,
            "language_names": language_names,
            "translations": translations,
            "metadata": metadata,
        }

        # 删除临时文件
        os.remove(file_path)

        return jsonify({"code": 0, "message": "文件解析成功", "data": result_data})

    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500


@translate_bp.route("/translate/batch", methods=["POST"])
def batch_translate():
    """批量翻译接口 - 支持单条和多条翻译"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({"code": -1, "message": "请求数据为空"}), 400

        # 验证必需参数
        if "items" not in request_data:
            return jsonify({"code": -1, "message": "缺少items参数"}), 400

        items = request_data["items"]
        if not isinstance(items, list) or len(items) == 0:
            return jsonify({"code": -1, "message": "items必须是非空数组"}), 400

        source_language = request_data.get("source_language", "en-US")

        translate_service = TranslateService()
        results = []

        logger.info(f"开始批量翻译，共 {len(items)} 条记录")

        # 处理每个翻译项
        for item in items:
            try:
                # 验证单个item的必需字段
                if (
                    "key" not in item
                    or "source_text" not in item
                    or "target_languages" not in item
                ):
                    results.append(
                        {
                            "key": item.get("key", "unknown"),
                            "success": False,
                            "error": "缺少必需字段: key, source_text, target_languages",
                        }
                    )
                    continue

                key = item["key"]
                source_text = item["source_text"]
                target_languages = item["target_languages"]

                if not isinstance(target_languages, list) or len(target_languages) == 0:
                    results.append(
                        {
                            "key": key,
                            "success": False,
                            "error": "target_languages必须是非空数组",
                        }
                    )
                    continue

                # 如果源文本为空，返回空翻译结果
                if not source_text or not source_text.strip():
                    translations = {lang: "" for lang in target_languages}
                    results.append(
                        {"key": key, "success": True, "translations": translations}
                    )
                    continue

                # 执行批量翻译
                translations = translate_service.translate_content_batch(
                    source_text=source_text,
                    target_languages=target_languages,
                    source_language=source_language,
                )

                results.append(
                    {"key": key, "success": True, "translations": translations}
                )

                logger.info(f"翻译完成: {key}")

            except Exception as e:
                logger.error(
                    f"翻译失败 - key: {item.get('key', 'unknown')}, error: {str(e)}"
                )
                results.append(
                    {
                        "key": item.get("key", "unknown"),
                        "success": False,
                        "error": str(e),
                    }
                )

        # 统计结果
        success_count = sum(1 for r in results if r["success"])
        total_count = len(results)

        logger.info(f"批量翻译完成: {success_count}/{total_count} 成功")

        return jsonify(
            {
                "code": 0,
                "message": f"翻译完成: {success_count}/{total_count} 成功",
                "data": {
                    "results": results,
                    "summary": {
                        "total": total_count,
                        "success": success_count,
                        "failed": total_count - success_count,
                    },
                },
            }
        )

    except Exception as e:
        logger.error(f"批量翻译处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500
