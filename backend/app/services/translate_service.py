import logging
import json
import time
from typing import List, Dict, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.llms import LLMType, get_llm

logger = logging.getLogger(__name__)


class TranslateService:

    def __init__(self, max_workers: int = 3):
        """
        初始化翻译服务

        Args:
            max_workers: 并发翻译的最大线程数，默认为3
        """
        self.max_workers = max_workers

    def translate_language_code_list(
        self, language_info_list: List[str], target_language: str = "zh-CN"
    ) -> Dict[str, str]:
        """
        翻译语言标识

        Args:
            language_code: 要翻译的语言代码，如 'en-US', 'zh-CN'
            target_language: 目标语言，默认为 'zh-CN'

        Returns:
            翻译后的语言名称
        """

        logger.info(language_info_list)

        # 将语言代码列表转换为字符串
        language_codes_text = ",".join([info["code"] for info in language_info_list])

        prompt_template = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """
你是一位语言专家。你会将用给到的地区语言code转为对应的中文语言标识，并且你会按照一定格式进行输出。比如：
用户输入：zh-CN,zh-TW,en-US
你会返回：{{
    \"zh-CN\": \"简体中文\",
    \"zh-TW\": \"繁体中文\",
    \"en-US\": \"英语\"
}}
""",
                ),
                ("user", "{text}"),
            ]
        )

        parser = StrOutputParser()

        llm = get_llm(llm_type=LLMType.TONGYI)

        chain = prompt_template | llm | parser

        try:
            result = chain.invoke({"text": language_codes_text})

            # 验证结果格式
            if isinstance(result, str):
                # 尝试解析JSON以验证格式
                try:
                    json.loads(result)
                    logger.info(f"语言代码翻译成功: {language_codes_text}")
                except json.JSONDecodeError:
                    logger.warning(f"语言代码翻译结果格式异常，但继续使用: {result}")

            return result

        except Exception as e:
            logger.error(f"语言代码翻译失败: {str(e)}")
            # 返回默认的语言代码映射
            fallback_result = {}
            for info in language_info_list:
                fallback_result[info["code"]] = info["code"]
            return json.dumps(fallback_result)

    def _translate_batch_task(
        self, task_info: Tuple[str, str, List[str], Dict, str]
    ) -> Tuple[str, Dict[str, str], str]:
        """
        批量翻译任务的辅助方法，用于并发处理

        Args:
            task_info: (key, source_text, target_languages, translate_items, source_language)

        Returns:
            (key, translations_dict, error_message)
        """
        key, source_text, target_languages, translate_items, source_language = task_info

        try:
            # 使用批量翻译方法
            translations = self.translate_content_batch(
                source_text=source_text,
                target_languages=target_languages,
                source_language=source_language,
            )

            # 检查翻译结果是否有效
            has_valid_translation = any(
                translations.get(lang, "").strip() for lang in target_languages
            )

            if has_valid_translation:
                return (key, translations, "")
            else:
                # 所有翻译都为空，视为失败
                logger.warning(f"任务 {key} 的所有翻译结果都为空")
                empty_translations = {lang: "" for lang in target_languages}
                return (key, empty_translations, "翻译结果为空")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"并发批量翻译任务异常 {key}: {error_msg}")
            # 返回未翻译状态（空字符串）
            empty_translations = {lang: "" for lang in target_languages}
            return (key, empty_translations, error_msg)

    def translate_content_list(self, info: Dict) -> Dict:
        """
        翻译内容列表

        Args:
            info: 包含翻译任务的字典，格式为：
                {
                    "source_langauge": "en-US",
                    "target_language_list": [{"code": "ja-JP", "name": ""}],
                    "list": [
                        {
                            "key": "hello",
                            "source_language": "en-US",
                            "source_language_context": "Hello",
                            "translate_list": [{"language": "ja-JP", "content": ""}]
                        }
                    ]
                }

        Returns:
            处理后的翻译数据
        """
        logger.info("开始处理翻译内容列表")

        # 先翻译target_language_list中的语言名称
        try:
            language_names_result = self.translate_language_code_list(
                info["target_language_list"]
            )
            # 解析JSON字符串结果
            if isinstance(language_names_result, str):
                language_names_dict = json.loads(language_names_result)
            else:
                language_names_dict = language_names_result

            # 更新语言名称
            for item in info["target_language_list"]:
                item["name"] = language_names_dict.get(item["code"], item["code"])

            logger.info(f"语言名称翻译完成: {language_names_dict}")
        except Exception as e:
            logger.warning(f"语言名称翻译失败，使用语言代码作为名称: {str(e)}")
            # 如果翻译失败，使用语言代码作为名称，但不中断流程
            for item in info["target_language_list"]:
                item["name"] = item["code"]

        # 翻译具体内容 - 使用并发处理
        source_language = info.get(
            "source_langauge", "en-US"
        )  # 注意这里的拼写错误，保持与原数据结构一致

        # 收集需要翻译的任务，按key分组
        translation_tasks = (
            []
        )  # 每个元素为 (key, source_text, target_languages, translate_items)

        for task in info.get("list", []):
            key = task.get("key", "")
            source_text = task.get("source_language_context", "")

            if not source_text:
                logger.warning(f"任务 {key} 的源文本为空，跳过翻译")
                continue

            # 收集该key需要翻译的目标语言
            target_languages = []
            translate_items = {}  # 语言代码 -> translate_item的映射

            for translate_item in task.get("translate_list", []):
                target_language = translate_item.get("language", "")
                current_content = translate_item.get("content", "")

                # 如果已有翻译内容，跳过（可以根据需要修改此逻辑）
                if current_content and current_content.strip():
                    logger.info(f"任务 {key} 的 {target_language} 已有翻译，跳过")
                    continue

                target_languages.append(target_language)
                translate_items[target_language] = translate_item

            # 如果有需要翻译的语言，添加到任务列表
            if target_languages:
                translation_tasks.append(
                    (
                        key,
                        source_text,
                        target_languages,
                        translate_items,
                        source_language,
                    )
                )

        logger.info(
            f"准备并发翻译 {len(translation_tasks)} 个任务，使用 {self.max_workers} 个线程"
        )

        # 使用线程池进行并发翻译
        translation_count = 0
        error_count = 0
        start_time = time.time()

        if translation_tasks:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有翻译任务
                future_to_task = {
                    executor.submit(self._translate_batch_task, task_info): task_info
                    for task_info in translation_tasks
                }

                # 处理完成的任务
                for future in as_completed(future_to_task):
                    try:
                        # 获取原始任务信息
                        original_task_info = future_to_task[future]
                        key, source_text, target_languages, translate_items, _ = (
                            original_task_info
                        )

                        try:
                            # 尝试获取翻译结果
                            key, translations_dict, error_message = future.result()
                        except Exception as future_error:
                            # 如果future本身出错，创建默认的失败结果
                            logger.error(f"任务 {key} 执行异常: {str(future_error)}")
                            translations_dict = {lang: "" for lang in target_languages}
                            error_message = f"任务执行异常: {str(future_error)}"

                        # 更新翻译结果
                        for target_language in target_languages:
                            translate_item = translate_items.get(target_language)
                            if translate_item:
                                translated_text = translations_dict.get(
                                    target_language, ""
                                )
                                translate_item["content"] = translated_text

                                if not error_message and translated_text.strip():
                                    translation_count += 1
                                    logger.debug(
                                        f"任务 {key} -> {target_language} 翻译完成: {translated_text}"
                                    )
                                else:
                                    error_count += 1
                                    if error_message:
                                        logger.warning(
                                            f"任务 {key} -> {target_language} 翻译失败: {error_message}"
                                        )
                                    else:
                                        logger.warning(
                                            f"任务 {key} -> {target_language} 翻译结果为空"
                                        )

                    except Exception as e:
                        # 这里是最后的兜底处理，确保不会因为单个任务而中断整个流程
                        error_count += 1
                        logger.error(f"处理翻译结果时发生未预期错误: {str(e)}")

                        # 尝试从future_to_task获取任务信息进行兜底处理
                        try:
                            original_task_info = future_to_task[future]
                            key, source_text, target_languages, translate_items, _ = (
                                original_task_info
                            )

                            # 为所有目标语言设置空的翻译结果
                            for target_language in target_languages:
                                translate_item = translate_items.get(target_language)
                                if translate_item and not translate_item.get("content"):
                                    translate_item["content"] = ""

                            logger.warning(f"任务 {key} 已设置为未翻译状态")
                        except Exception as fallback_error:
                            logger.error(f"兜底处理也失败: {str(fallback_error)}")
                            # 即使兜底处理失败，也要继续处理其他任务

        # 计算统计信息
        end_time = time.time()
        total_time = end_time - start_time
        total_tasks = len(translation_tasks)
        success_rate = (translation_count / total_tasks * 100) if total_tasks > 0 else 0

        logger.info(
            f"并发翻译完成 - 总任务: {total_tasks}, 成功: {translation_count}, "
            f"失败: {error_count}, 成功率: {success_rate:.1f}%, 耗时: {total_time:.2f}秒"
        )
        return info

    def translate_content_batch(
        self,
        source_text: str,
        target_languages: List[str],
        source_language: str = "en-US",
    ) -> Dict[str, str]:
        """
        在一次对话中翻译文本到多个目标语言

        Args:
            source_text: 要翻译的源文本
            target_languages: 目标语言代码列表，如 ['zh-CN', 'ja-JP', 'ko-KR']
            source_language: 源语言代码，默认为 'en-US'

        Returns:
            字典，键为目标语言代码，值为翻译结果
        """
        if not source_text or not source_text.strip():
            return {lang: "" for lang in target_languages}

        if not target_languages:
            return {}

        logger.info(
            f"批量翻译文本: {source_text} ({source_language} -> {target_languages})"
        )

        # 构建目标语言列表字符串
        target_langs_str = ", ".join(target_languages)

        # 构建翻译提示模板
        prompt_template = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """
你是一位专业的翻译专家。请将给定的文本从源语言准确翻译为多个目标语言。

翻译要求：
1. 保持原文的意思和语调
2. 使用自然流畅的目标语言表达
3. 保留原文的格式和标点符号
4. 如果是专业术语，请使用准确的对应词汇
5. 返回JSON格式的结果，键为语言代码，值为翻译结果

源语言: {source_language}
目标语言: {target_languages}

请按照以下JSON格式返回翻译结果：
{{
    "zh-CN": "中文翻译的内容,
    "ja-JP": "日语翻译的内容",
    "ko-KR": "韩语翻译的内容"
}}
""",
                ),
                ("user", "{text}"),
            ]
        )

        parser = StrOutputParser()
        llm = get_llm(llm_type=LLMType.TONGYI)
        chain = prompt_template | llm | parser

        # 直接执行翻译，不进行重试
        try:
            result = chain.invoke(
                {
                    "text": source_text,
                    "source_language": source_language,
                    "target_languages": target_langs_str,
                }
            )

            if not result or not result.strip():
                logger.error(f"批量翻译结果为空: {source_text}")
                return {lang: "" for lang in target_languages}

            # 解析JSON结果
            logger.debug(result)
            try:
                translations = json.loads(result)

                # 验证结果包含所有目标语言
                final_result = {}
                for lang in target_languages:
                    if lang in translations and translations[lang]:
                        final_result[lang] = translations[lang].strip()
                    else:
                        # 如果某个语言缺失，返回空字符串
                        final_result[lang] = ""
                        logger.warning(f"语言 {lang} 的翻译结果缺失")

                logger.info(f"批量翻译完成: {source_text} -> {final_result}")
                return final_result

            except json.JSONDecodeError as e:
                logger.error(f"翻译结果JSON格式错误: {str(e)}")
                return {lang: "" for lang in target_languages}

        except Exception as e:
            error_type = type(e).__name__
            logger.error(f"批量翻译失败 ({error_type}): {str(e)}")
            return {lang: "" for lang in target_languages}

    def translate_content(
        self,
        source_text: str,
        target_language: str,
        source_language: str = "en-US",
    ) -> str:
        """
        翻译具体的内容文本

        Args:
            source_text: 要翻译的源文本
            source_language: 源语言代码，默认为 'en-US'
            target_language: 目标语言代码，默认为 'zh-CN'

        Returns:
            翻译后的内容
        """
        if not source_text or not source_text.strip():
            return ""

        logger.info(f"翻译文本: {source_text} ({source_language} -> {target_language})")

        # 构建翻译提示模板
        prompt_template = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """
你是一位专业的翻译专家。请将给定的文本从源语言准确翻译为目标语言。

翻译要求：
1. 保持原文的意思和语调
2. 使用自然流畅的目标语言表达
3. 保留原文的格式和标点符号
4. 如果是专业术语，请使用准确的对应词汇
5. 只返回翻译结果，不要添加任何解释

源语言: {source_language}
目标语言: {target_language}
""",
                ),
                ("user", "{text}"),
            ]
        )

        parser = StrOutputParser()
        llm = get_llm(llm_type=LLMType.TONGYI)
        chain = prompt_template | llm | parser

        # 直接执行翻译，不进行重试
        try:
            result = chain.invoke(
                {
                    "text": source_text,
                    "source_language": source_language,
                    "target_language": target_language,
                }
            )

            if not result or not result.strip():
                logger.error(f"翻译结果为空: {source_text}")
                return ""

            logger.info(f"翻译完成: {source_text} -> {result}")
            return result.strip()

        except Exception as e:
            error_type = type(e).__name__
            logger.error(f"翻译失败 ({error_type}): {str(e)}")
            return ""
